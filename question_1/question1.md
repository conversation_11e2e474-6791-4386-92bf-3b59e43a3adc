Question 1:
Using Java or Python, implement an idempotency key mechanism for a RESTful API that processes
payment transactions.
Your implementation should:
1. Accept an idempotency key in API requests
2. Store the request and response associated with each idempotency key
3. Return the payment transaction response when the same idempotency key is reused
4. Handle concurrent requests with the same idempotency key correctly
5. Set appropriate expiration for stored idempotency keys