Question 2:
Using Java or Python, implement a notification system using that can:
1. Support multiple notification channels including Email and SMS
2. Select the appropriate notification channel based on user preferences like if user enable Email as
their notification option, use Email

* Note: Assumption: Email and SMS provider will be available via 3rd party providers so you don’t need to write
the actual code to send the notification through those channels