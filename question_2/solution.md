### Implementation

```python
# ---- models.py ----
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional
from datetime import datetime
import time


class NotificationChannel(Enum):
    """Supported notification channels."""
    EMAIL = "email"
    SMS = "sms"


class NotificationStatus(Enum):
    """Status of notification delivery."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"


@dataclass
class UserPreferences:
    """User notification preferences."""
    user_id: str
    enabled_channels: List[NotificationChannel]
    email_address: Optional[str] = None
    phone_number: Optional[str] = None
    
    def is_channel_enabled(self, channel: NotificationChannel) -> bool:
        """Check if a notification channel is enabled for the user."""
        return channel in self.enabled_channels
    
    def get_contact_info(self, channel: NotificationChannel) -> Optional[str]:
        """Get contact information for a specific channel."""
        if channel == NotificationChannel.EMAIL:
            return self.email_address
        elif channel == NotificationChannel.SMS:
            return self.phone_number
        return None


@dataclass
class NotificationMessage:
    """Notification message data."""
    message_id: str
    user_id: str
    subject: str
    content: str
    channel: NotificationChannel
    recipient: str
    created_at: datetime
    status: NotificationStatus = NotificationStatus.PENDING
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass
class NotificationRequest:
    """Request to send a notification."""
    user_id: str
    subject: str
    content: str
    preferred_channels: Optional[List[NotificationChannel]] = None

    def __post_init__(self):
        """Validate request data after initialization."""
        if not self.user_id or not isinstance(self.user_id, str) or not self.user_id.strip():
            raise ValueError("user_id is required and must be a non-empty string")

        if not self.subject or not isinstance(self.subject, str) or not self.subject.strip():
            raise ValueError("subject is required and must be a non-empty string")

        if not self.content or not isinstance(self.content, str) or not self.content.strip():
            raise ValueError("content is required and must be a non-empty string")



        if self.preferred_channels is not None:
            if not isinstance(self.preferred_channels, list):
                raise ValueError("preferred_channels must be a list")

            for channel in self.preferred_channels:
                if not isinstance(channel, NotificationChannel):
                    raise ValueError("preferred_channels must contain only NotificationChannel values")


# ---- interfaces.py ----
from abc import ABC, abstractmethod
import uuid
import logging


class NotificationChannelProvider(ABC):
    """Abstract base class for notification channel providers."""

    @property
    @abstractmethod
    def channel_type(self) -> NotificationChannel:
        """Return the channel type this provider handles."""
        pass

    @abstractmethod
    def send_notification(self, message: NotificationMessage) -> bool:
        """Send a notification message through this channel."""
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """Check if the notification channel is currently available."""
        pass


class UserPreferenceRepository(ABC):
    """Abstract repository for user preferences."""
    
    @abstractmethod
    def get_user_preferences(self, user_id: str) -> Optional[UserPreferences]:
        """Get user notification preferences."""
        pass
    
    @abstractmethod
    def save_user_preferences(self, preferences: UserPreferences) -> bool:
        """Save user notification preferences."""
        pass


class NotificationRepository(ABC):
    """Abstract repository for notification messages."""
    
    @abstractmethod
    def save_notification(self, message: NotificationMessage) -> bool:
        """Save a notification message."""
        pass
    
    @abstractmethod
    def get_notification(self, message_id: str) -> Optional[NotificationMessage]:
        """Get a notification message by ID."""
        pass
    
    @abstractmethod
    def update_notification_status(self, message_id: str, status: str,
                                 error_message: Optional[str] = None) -> bool:
        """Update notification status."""
        pass


# ---- providers.py ----
import logging


logger = logging.getLogger(__name__)


class EmailProvider(NotificationChannelProvider):
    """Email notification provider (mocks 3rd party email service like SendGrid)."""

    def __init__(self, api_key: str = "mock_email_api_key"):
        """Initialize email provider."""
        self.api_key = api_key
        self._is_available = True
    
    @property
    def channel_type(self) -> NotificationChannel:
        """Return the channel type this provider handles."""
        return NotificationChannel.EMAIL
    


    def send_notification(self, message: NotificationMessage) -> bool:
        """Send an email notification."""
        try:
            # Validate message
            if not message.recipient or '@' not in message.recipient:
                logger.error(f"Invalid email address: {message.recipient}")
                return False

            # Email sending logic would go here
            # Example: response = email_client.send(to=message.recipient, subject=message.subject, body=message.content)

            logger.info(f"Email sent successfully to {message.recipient} for message {message.message_id}")
            return True

        except Exception as e:
            logger.error(f"Email send failed for message {message.message_id}: {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if the email service is available."""
        return self._is_available
    
    def set_availability(self, available: bool):
        """Set service availability (for testing purposes)."""
        self._is_available = available


class SMSProvider(NotificationChannelProvider):
    """SMS notification provider (mocks 3rd party SMS service like Twilio)."""

    def __init__(self, account_sid: str = "mock_sms_account_sid",
                 auth_token: str = "mock_sms_auth_token"):
        """
        Initialize SMS provider.
        """
        self.account_sid = account_sid
        self.auth_token = auth_token
        self._is_available = True
    
    @property
    def channel_type(self) -> NotificationChannel:
        """Return the channel type this provider handles."""
        return NotificationChannel.SMS

    def send_notification(self, message: NotificationMessage) -> bool:
        """Send an SMS notification."""
        try:
            # Validate phone number
            if not message.recipient or not message.recipient.startswith('+'):
                logger.error(f"Invalid phone number: {message.recipient}")
                return False

            # SMS sending logic would go here
            # Example: response = sms_client.send(to=message.recipient, body=message.content)

            logger.info(f"SMS sent successfully to {message.recipient} for message {message.message_id}")
            return True

        except Exception as e:
            logger.error(f"SMS send failed for message {message.message_id}: {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if the SMS service is available."""
        return self._is_available
    
    def set_availability(self, available: bool):
        """Set service availability (for testing purposes)."""
        self._is_available = available


# ---- repositories.py ----


class InMemoryUserPreferenceRepository(UserPreferenceRepository):
    """In-memory implementation of user preference repository."""

    def __init__(self):
        self._preferences: Dict[str, UserPreferences] = {}

    def get_user_preferences(self, user_id: str) -> Optional[UserPreferences]:
        """Get user notification preferences."""
        return self._preferences.get(user_id)

    def save_user_preferences(self, preferences: UserPreferences) -> bool:
        """Save user notification preferences."""
        try:
            self._preferences[preferences.user_id] = preferences
            logger.info(f"Saved preferences for user {preferences.user_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to save preferences for user {preferences.user_id}: {e}")
            return False


class InMemoryNotificationRepository(NotificationRepository):
    """In-memory implementation of notification repository."""

    def __init__(self):
        self._notifications: Dict[str, NotificationMessage] = {}

    def save_notification(self, message: NotificationMessage) -> bool:
        """Save a notification message."""
        try:
            self._notifications[message.message_id] = message
            logger.info(f"Saved notification {message.message_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to save notification {message.message_id}: {e}")
            return False

    def get_notification(self, message_id: str) -> Optional[NotificationMessage]:
        """Get a notification message by ID."""
        return self._notifications.get(message_id)

    def update_notification_status(self, message_id: str, status: str,
                                 error_message: Optional[str] = None) -> bool:
        """Update notification status."""
        try:
            message = self._notifications.get(message_id)
            if not message:
                logger.warning(f"Notification {message_id} not found for status update")
                return False

            # Update status
            if status == "sent":
                message.status = NotificationStatus.SENT
                message.sent_at = datetime.now()
            elif status == "delivered":
                message.status = NotificationStatus.DELIVERED
                message.delivered_at = datetime.now()
            elif status == "failed":
                message.status = NotificationStatus.FAILED
                message.error_message = error_message

            logger.info(f"Updated notification {message_id} status to {status}")
            return True
        except Exception as e:
            logger.error(f"Failed to update notification {message_id} status: {e}")
            return False


# ---- notification_service.py ----
import uuid


class NotificationService:
    """
    Main notification service that handles sending notifications through
    appropriate channels based on user preferences.
    """

    def __init__(self,
                 user_preference_repo: UserPreferenceRepository,
                 notification_repo: NotificationRepository):
        """
        Initialize the notification service.
        """
        self.user_preference_repo = user_preference_repo
        self.notification_repo = notification_repo
        self.channel_providers: Dict[NotificationChannel, NotificationChannelProvider] = {}

    def register_channel_provider(self, provider: NotificationChannelProvider):
        """Register a notification channel provider."""
        self.channel_providers[provider.channel_type] = provider
        logger.info(f"Registered {provider.channel_type.value} provider")

    def send_notification(self, request: NotificationRequest) -> List[str]:
        """Send a notification based on user preferences."""
        # Input validation is handled by NotificationRequest.__post_init__

        # Get user preferences
        user_preferences = self.user_preference_repo.get_user_preferences(request.user_id)
        if not user_preferences:
            logger.warning(f"No preferences found for user {request.user_id}")
            return []

        # Determine which channels to use
        channels_to_use = self._select_channels(user_preferences, request.preferred_channels)
        if not channels_to_use:
            logger.warning(f"No available channels for user {request.user_id}")
            return []

        sent_message_ids = []

        # Send notification through each selected channel
        for channel in channels_to_use:
            message_id = self._send_through_channel(channel, user_preferences, request)
            if message_id:
                sent_message_ids.append(message_id)

        return sent_message_ids



    def _send_through_channel(self,
                             channel: NotificationChannel,
                             user_preferences: UserPreferences,
                             request: NotificationRequest) -> Optional[str]:
        """Send notification through a specific channel."""
        provider = self.channel_providers.get(channel)
        if not provider:
            logger.error(f"No provider registered for {channel.value}")
            return None

        # Get recipient contact info
        recipient = user_preferences.get_contact_info(channel)
        if not recipient:
            logger.error(f"No contact info for {channel.value} for user {request.user_id}")
            return None

        # Create notification message
        message_id = str(uuid.uuid4())
        message = NotificationMessage(
            message_id=message_id,
            user_id=request.user_id,
            subject=request.subject,
            content=request.content,
            channel=channel,
            recipient=recipient,
            created_at=datetime.now()
        )

        # Save message to repository
        if not self.notification_repo.save_notification(message):
            logger.error(f"Failed to save notification {message_id}")
            return None

        # Send through provider
        try:
            success = provider.send_notification(message)
            if success:
                self.notification_repo.update_notification_status(message_id, "sent")
                logger.info(f"Notification {message_id} sent via {channel.value}")
                return message_id
            else:
                self.notification_repo.update_notification_status(
                    message_id, "failed", "Provider send failed"
                )
                logger.error(f"Failed to send notification {message_id} via {channel.value}")
                return None
        except Exception as e:
            self.notification_repo.update_notification_status(
                message_id, "failed", str(e)
            )
            logger.error(f"Exception sending notification {message_id}: {e}")
            return None

    def _select_channels(self,
                        user_preferences: UserPreferences,
                        preferred_channels: Optional[List[NotificationChannel]]) -> List[NotificationChannel]:
        """Select appropriate channels based on user preferences and request."""
        # Start with user's enabled channels
        available_channels = user_preferences.enabled_channels.copy()

        # If specific channels are requested, filter to those
        if preferred_channels:
            available_channels = [
                channel for channel in available_channels
                if channel in preferred_channels
            ]

        # Filter to channels that have registered providers and are available
        usable_channels = []
        for channel in available_channels:
            provider = self.channel_providers.get(channel)
            if provider and provider.is_available():
                # Check if user has contact info for this channel
                contact_info = user_preferences.get_contact_info(channel)
                if contact_info:
                    usable_channels.append(channel)
                else:
                    logger.warning(f"No contact info for {channel.value} for user {user_preferences.user_id}")
            else:
                logger.warning(f"Provider for {channel.value} not available")

        return usable_channels

    def get_notification_status(self, message_id: str) -> Optional[NotificationMessage]:
        """Get the status of a notification."""
        return self.notification_repo.get_notification(message_id)

```